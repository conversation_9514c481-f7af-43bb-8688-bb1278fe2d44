// 音频处理类动作定�?

import { CategoryIds, ActionIds, AudioFormats } from "../../constants";
import type { Action } from "../../types/action";
import { applyActionMapping } from "../action-mappings";

// 音频处理类动作定�?
const rawAudioActions = [
  {
    id: ActionIds.ADJUST_VOLUME,
    nameKey: "actions.adjust_volume.name",
    descriptionKey: "actions.adjust_volume.description",
    categoryId: CategoryIds.AUDIO,
    inputTypes: ["video", "audio"],
    outputTypes: ["video", "audio"],
    preview: true,
    order: 10,
    params: [
      {
        key: "volume",
        type: "range",
        nameKey: "actions.adjust_volume.params.volume",
        required: true,
        defaultValue: 1.0,
        min: 0,
        max: 3,
        step: 0.1,
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      const volume = parseFloat(params.volume);
      if (isNaN(volume)) {
        errors.push("actions.adjust_volume.errors.invalid_number");
      } else if (volume < 0 || volume > 3) {
        errors.push("actions.adjust_volume.errors.invalid_range");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.EXTRACT_AUDIO,
    nameKey: "actions.extract_audio.name",
    descriptionKey: "actions.extract_audio.description",
    categoryId: CategoryIds.AUDIO,
    inputTypes: ["video"],
    outputTypes: ["audio"],
    preview: false,
    order: 20,
    params: [
      {
        key: "format",
        type: "select",
        nameKey: "actions.extract_audio.params.format",
        required: true,
        defaultValue: AudioFormats.MP3,
        options: [
          { value: AudioFormats.MP3, labelKey: "formats.audio.mp3" },
          { value: AudioFormats.WAV, labelKey: "formats.audio.wav" },
          { value: AudioFormats.AAC, labelKey: "formats.audio.aac" },
          { value: AudioFormats.FLAC, labelKey: "formats.audio.flac" },
          { value: AudioFormats.OGG, labelKey: "formats.audio.ogg" },
          { value: AudioFormats.M4A, labelKey: "formats.audio.m4a" },
        ],
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      const validFormats = Object.values(AudioFormats);
      if (!validFormats.includes(params.format)) {
        errors.push("actions.extract_audio.errors.invalid_format");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.ADD_BACKGROUND_MUSIC,
    nameKey: "actions.add_background_music.name",
    descriptionKey: "actions.add_background_music.description",
    categoryId: CategoryIds.AUDIO,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 30,
    params: [
      {
        key: "audioFile",
        type: "file",
        nameKey: "actions.add_background_music.params.audioFile",
        required: true,
        defaultValue: "",
      },
      {
        key: "loopBgm",
        type: "boolean",
        nameKey: "actions.add_background_music.params.loopBgm",
        required: false,
        defaultValue: true,
      },
      {
        key: "bgmVolume",
        type: "range",
        nameKey: "actions.add_background_music.params.bgmVolume",
        required: true,
        defaultValue: 0.5,
        min: 0,
        max: 1,
        step: 0.1,
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      if (!params.audioFile || params.audioFile.trim() === "") {
        errors.push("actions.add_background_music.errors.no_audio_file");
      }

      const bgmVolume = parseFloat(params.bgmVolume);
      if (isNaN(bgmVolume)) {
        errors.push(
          "actions.add_background_music.errors.invalid_volume_number"
        );
      } else if (bgmVolume < 0 || bgmVolume > 1) {
        errors.push("actions.add_background_music.errors.invalid_volume");
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.REPLACE_AUDIO,
    nameKey: "actions.replace_audio.name",
    descriptionKey: "actions.replace_audio.description",
    categoryId: CategoryIds.AUDIO,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 40,
    params: [
      {
        key: "audioFile",
        type: "file",
        nameKey: "actions.replace_audio.params.audioFile",
        required: true,
        defaultValue: "",
      },
      {
        key: "loopAudio",
        type: "boolean",
        nameKey: "actions.replace_audio.params.loopAudio",
        required: false,
        defaultValue: true,
      },
      {
        key: "audioVolume",
        type: "range",
        nameKey: "actions.replace_audio.params.audioVolume",
        required: true,
        defaultValue: 1.0,
        min: 0,
        max: 3,
        step: 0.1,
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      if (!params.audioFile || params.audioFile.trim() === "") {
        errors.push("actions.replace_audio.errors.no_audio_file");
      }

      const audioVolume = parseFloat(params.audioVolume);
      if (isNaN(audioVolume)) {
        errors.push("actions.replace_audio.errors.invalid_volume_number");
      } else if (audioVolume < 0 || audioVolume > 3) {
        errors.push("actions.replace_audio.errors.invalid_volume");
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.MUTE_AUDIO,
    nameKey: "actions.mute_audio.name",
    descriptionKey: "actions.mute_audio.description",
    categoryId: CategoryIds.AUDIO,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 50,
    params: [],
  },
];

// 应用映射并导�?
export const audioActions: Action[] = rawAudioActions.map(applyActionMapping);
